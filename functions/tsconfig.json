{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noEmit": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "types": ["node", "@types/aws-lambda"], "allowImportingTsExtensions": true, "moduleDetection": "force", "outDir": "./dist", "rootDir": "./src", "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false}, "include": ["src/**/*"], "exclude": ["node_modules", ".serverless", "dist"]}