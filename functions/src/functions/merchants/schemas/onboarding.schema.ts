import { z } from "zod";
import { phoneSchema, emailSchema, einSchema, zipSchema, mccSchema, tcDateSchema } from "./validation-helpers.schema.js";
import { accountsSchema } from "./account.schema.js";
import { memberSchema } from "./member.schema.js";

// Business type schema based on official Payrix API specification
// Valid values: 0-8 representing different business types
const businessTypeSchema = z.union([
  z.literal(0), // Sole Proprietorship
  z.literal(1), // Corporation
  z.literal(2), // Limited Liability Company
  z.literal(3), // Partnership
  z.literal(4), // Association
  z.literal(5), // Non-Profit Organization
  z.literal(6), // Government Organization
  z.literal(7), // C Corporation
  z.literal(8), // S Corporation
]);

const bankVerificationFileSchema = z.object({
  name: z.string().min(1, "File name is required"),
  size: z.number().positive("File size must be positive"),
  type: z.string().min(1, "File type is required"),
  content: z.string().min(1, "File content is required"),
});

const plaidDataSchema = z.object({
  publicToken: z.string().min(1, "Public token is required"),
  accountToken: z.string().min(1, "Account token is required"),
  institutionName: z.string().optional(),
  accountMask: z.string().optional(),
});

const bankVerificationSchema = z.object({
  verificationMethod: z.enum(["manual", "plaid"]),
  verificationFile: bankVerificationFileSchema.optional(),
  plaidData: plaidDataSchema.optional(),
});

const userAccountSchema = z.object({
  createAccount: z.boolean(),
  username: z.string().min(1, "Username is required for account creation"),
  password: z.string().min(1, "Password is required for account creation"),
  confirmPassword: z.string().optional(),
});

// Merchant schema for nested merchant object (matches working implementation)
const merchantSchema = z.object({
  dba: z.string().optional(),
  new: z.number().int().min(0).max(1).default(1),
  mcc: mccSchema,
  status: z.string().default("1"),
  annualCCSales: z.number().int().min(0).optional().default(0),
  avgTicket: z.number().int().min(0).optional().default(50),
  established: z.string().optional(),
  members: z.array(memberSchema).min(1, "At least one member is required"), // Members nested under merchant
});

const onboardingSchema = z.object({
  name: z.string().min(1, "Business legal name is required"),
  dba: z.string().optional(),
  type: businessTypeSchema, // Business type (0-8 as per Payrix API spec)
  ein: einSchema,
  address1: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().length(2, "State must be 2 characters"),
  zip: zipSchema,
  country: z.string().length(3, "Country must be 3 characters").default("USA"),
  phone: phoneSchema,
  email: emailSchema,
  website: z.string().url("Invalid website URL").optional(),
  cashdiscount: z.number().int().min(0).max(1).default(0),
  mcc: mccSchema,
  accounts: accountsSchema.optional(),
  merchant: merchantSchema, // Nested merchant object containing members (matches payload structure)

  // Additional fields from working implementation
  public: z.number().int().min(0).max(1).default(0),
  status: z.number().int().default(1),
  currency: z.string().default("USD"),

  // Terms and compliance fields
  tcDate: tcDateSchema,
  tcVersion: z.string().min(1, "Terms version is required"),
  tcIp: z.string().optional(),
  clientIp: z.string().optional(),
  tcAttestation: z.number().int().min(0).max(1).optional(),
  visaDisclosure: z.number().int().min(0).max(1).optional(),
  disclosureIP: z.string().optional(),
  disclosureDate: z.string().optional(),
  merchantIp: z.string().optional(),

  // User account creation fields (nested structure for backend compatibility)
  credentialId: z.string().optional(),
  partner: z.string().optional(),
  estimated: z.string().optional(),
  annualProcessing: z.string().optional(),
  monthlyProcessing: z.string().optional(),
  averageTicket: z.string().optional(),
  maxTransactionAmount: z.string().optional(),
  referrer: z.string().optional(),
  referrerEntity: z.string().optional(),
  bankVerification: bankVerificationSchema.optional(),
  userAccount: userAccountSchema.optional(),
});

export type OnboardingRequest = z.infer<typeof onboardingSchema>;

export function validateOnboardingRequest(data: unknown): {
  success: boolean;
  data?: OnboardingRequest;
  errors?: string[];
} {
  const result = onboardingSchema.safeParse(data);

  if (!result.success) {
    const errors = result.error.errors.map((err) => {
      const path = err.path.join(".");
      return path ? `${path}: ${err.message}` : err.message;
    });

    return {
      success: false,
      errors,
    };
  }

  return {
    success: true,
    data: result.data,
  };
}
