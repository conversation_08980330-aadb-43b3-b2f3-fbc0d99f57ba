import { AxiosError } from "axios";
import { PayrixService } from "../../../service/payrix.service.js";
import { logger } from "../../../helpers/logger.js";
import { validateVerificationFile } from "./file-validation.service.js";

export interface VerificationFile {
  name: string;
  size: number;
  type: string;
  content: string;
}

export interface VerificationUploadResult {
  success: boolean;
  noteId?: string;
  documentId?: string;
  error?: string;
}

export async function processBankVerification(
  payrixService: PayrixService,
  entityId: string,
  verificationFile: VerificationFile,
  requestId: string,
  verificationMethod: string,
  loginId?: string
): Promise<VerificationUploadResult> {
  if (verificationMethod !== "manual") {
    logger.info("Skipping bank verification upload - not manual verification", {
      requestId,
      entityId,
      verificationMethod,
    });
    return {
      success: true,
      noteId: undefined,
      documentId: undefined,
    };
  }

  if (!verificationFile) {
    logger.warn("Manual verification selected but no file provided", {
      requestId,
      entityId,
      verificationMethod,
    });
    throw new Error("Manual verification requires a file upload");
  }

  validateVerificationFile(verificationFile, requestId, entityId);

  logger.info("Starting priority bank verification upload process for manual verification", {
    requestId,
    entityId,
    verificationMethod,
    fileName: verificationFile.name,
    fileSize: verificationFile.size,
    fileType: verificationFile.type,
    hasLoginContext: !!loginId,
    loginId: loginId ? `${loginId.substring(0, 8)}...` : undefined,
  });

  try {
    const noteId = await createVerificationNote(payrixService, entityId, loginId, requestId);
    const documentId = await uploadVerificationDocument(
      payrixService,
      noteId,
      verificationFile,
      requestId,
      entityId
    );

    logger.info("Bank verification upload completed successfully - priority operation complete", {
      requestId,
      entityId,
      noteId,
      documentId,
      fileName: verificationFile.name,
      fileSize: verificationFile.size,
    });

    return {
      success: true,
      noteId,
      documentId,
    };
  } catch (error) {
    handleVerificationError(error as AxiosError, requestId, entityId);
    throw error;
  }
}

async function createVerificationNote(
  payrixService: PayrixService,
  entityId: string,
  loginId: string | undefined,
  requestId: string
): Promise<string> {
  if (!loginId) {
    logger.warn("Creating note without login context - user account creation may have failed", {
      requestId,
      entityId,
      step: "note_creation_without_login_context",
    });
  }

  logger.info("Creating verification note in Payrix", {
    requestId,
    entityId,
    hasLoginContext: !!loginId,
    step: "note_creation_start",
  });

  const noteData = {
    entity: entityId,
    note: `Bank Account Verification Document - Voided Check uploaded during merchant onboarding for entity ${entityId}`,
    type: "note",
    ...(loginId && { login: loginId }),
  };

  logger.info("Note data prepared", {
    requestId,
    entityId,
    noteType: noteData.type,
    noteLength: noteData.note.length,
    hasLoginContext: !!loginId,
    loginId: loginId ? `${loginId.substring(0, 8)}...` : undefined,
    step: "note_data_prepared",
  });

  const noteResponse = await payrixService.createNote(noteData);

  if (!noteResponse?.id) {
    logger.error("Failed to create verification note - no ID returned from Payrix", {
      requestId,
      entityId,
      noteResponse,
    });
    throw new Error("Failed to create verification note - Payrix did not return note ID");
  }

  logger.info("Verification note created successfully", {
    requestId,
    entityId,
    noteId: noteResponse.id,
  });

  return noteResponse.id;
}

async function uploadVerificationDocument(
  payrixService: PayrixService,
  noteId: string,
  verificationFile: VerificationFile,
  requestId: string,
  entityId: string
): Promise<string> {
  logger.info("Uploading verification document to Payrix", {
    requestId,
    entityId,
    noteId,
    fileName: verificationFile.name,
    step: "document_upload_start",
  });

  const fileBuffer = Buffer.from(verificationFile.content, "base64");

  logger.info("File buffer created", {
    requestId,
    entityId,
    noteId,
    bufferSize: fileBuffer.length,
    originalSize: verificationFile.size,
    step: "file_buffer_created",
  });

  const documentData = {
    note: noteId,
    file: {
      filename: verificationFile.name,
      content: fileBuffer,
      contentType: verificationFile.type,
    },
    description: `Voided check for bank account verification - ${verificationFile.name}`,
  };

  logger.info("Document data prepared", {
    requestId,
    entityId,
    noteId,
    documentData: {
      ...documentData,
      file: {
        ...documentData.file,
        content: `[Buffer ${fileBuffer.length} bytes]`,
      },
    },
    step: "document_data_prepared",
  });

  const documentResponse = await payrixService.createNoteDocument(documentData);

  if (!documentResponse?.id) {
    logger.error("Failed to create note document - no ID returned from Payrix", {
      requestId,
      entityId,
      noteId,
      documentResponse,
    });
    throw new Error("Failed to create note document - Payrix did not return document ID");
  }

  return documentResponse.id;
}

function handleVerificationError(error: AxiosError, requestId: string, entityId: string): void {
  if (error.response?.status === 400) {
    logger.error("Bad request to Payrix API for bank verification", {
      requestId,
      entityId,
      error: error.response.data,
    });
    throw new Error("Invalid data provided for bank verification upload");
  } else if (error.response?.status === 401) {
    logger.error("Authentication failed with Payrix API for bank verification", {
      requestId,
      entityId,
    });
    throw new Error("Authentication failed with Payrix API");
  } else if (error.response?.status === 404) {
    logger.error("Entity not found in Payrix for bank verification", {
      requestId,
      entityId,
    });
    throw new Error(`Entity ${entityId} not found in Payrix system`);
  }

  logger.error("Priority bank verification upload failed", {
    requestId,
    entityId,
    error,
  });
}