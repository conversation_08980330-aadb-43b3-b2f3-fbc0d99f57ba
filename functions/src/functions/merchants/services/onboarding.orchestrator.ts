import { PayrixService } from "../../../service/payrix.service.js";
import { logger } from "../../../helpers/logger.js";
import { OnboardingRequest } from "../schemas/onboarding.schema.js";
import { createUserAccountIfRequested } from "./user-account.service.js";
import { processBankVerification, VerificationFile, VerificationUploadResult } from "./bank-verification.service.js";
import { processPlaidVerification, prepareMerchantDataForPlaid } from "./plaid-verification.service.js";
import { buildSuccessResponse } from "../utils/response-builders.js";
import { handlePayrixError } from "../utils/error-handling.js";

import type { APIGatewayProxyResult } from "aws-lambda";

export interface OnboardingResult {
  success: boolean;
  response?: APIGatewayProxyResult;
  error?: string;
}

function prepareMerchantData(data: OnboardingRequest, requestId: string): OnboardingRequest {
  let merchantData = { ...data };

  if (data.bankVerification?.verificationMethod === "plaid" && data.bankVerification.plaidData) {
    merchantData = prepareMerchantDataForPlaid(merchantData, data.bankVerification.plaidData, requestId);
  }

  return merchantData;
}

async function createMerchantInPayrix(
  payrixService: PayrixService,
  merchantData: OnboardingRequest,
  requestId: string
): Promise<{ id: string; [key: string]: unknown }> {
  logger.info("Creating merchant in Payrix (direct integration)", { requestId });
  return await payrixService.createMerchant(merchantData);
}

async function createUserAccount(
  data: OnboardingRequest,
  payrixEntityId: string,
  requestId: string
): Promise<{
  success: boolean;
  data?: { id: string; email: string; sanitizedUsername?: string; originalUsername?: string; [key: string]: unknown } | null;
  error?: string;
}> {
  try {
    logger.info("Processing user account creation (before bank verification)", {
      requestId,
      payrixEntityId,
    });

    const result = await createUserAccountIfRequested(data, payrixEntityId, requestId);

    if (result.success) {
      logger.info("User account created successfully - login context available for bank verification", {
        requestId,
        payrixEntityId,
        userId: result.data?.id,
        hasLoginId: !!result.data?.id,
      });
    } else {
      logger.warn("User account creation failed - bank verification will proceed without login context", {
        requestId,
        payrixEntityId,
        error: result.error,
        impact: "Bank verification notes will be created without user login context",
      });
    }

    return result;
  } catch (userAccountError) {
    logger.error("User account creation failed with exception - bank verification will proceed without login context", {
      requestId,
      payrixEntityId,
      error: userAccountError,
      impact: "Bank verification notes will be created without user login context",
    });

    return {
      success: false,
      error: "User account creation failed",
    };
  }
}

async function processVerification(
  data: OnboardingRequest,
  payrixService: PayrixService,
  payrixEntityId: string,
  requestId: string,
  userAccountId?: string
): Promise<VerificationUploadResult | null> {
  if (!data.bankVerification?.verificationMethod) {
    return null;
  }

  const verificationMethod = data.bankVerification.verificationMethod;

  if (verificationMethod === "manual" && data.bankVerification.verificationFile) {
    return await processManualVerification(
      data.bankVerification.verificationFile as VerificationFile,
      payrixService,
      payrixEntityId,
      requestId,
      verificationMethod,
      userAccountId
    );
  }

  if (verificationMethod === "plaid") {
    return await processPlaidVerification(payrixService, payrixEntityId, data.bankVerification.plaidData, requestId, userAccountId);
  }

  logger.warn("Invalid bank verification configuration", {
    requestId,
    payrixEntityId,
    verificationMethod,
    hasFile: !!data.bankVerification.verificationFile,
  });

  return {
    success: false,
    error: "Invalid bank verification configuration",
  };
}

async function processManualVerification(
  verificationFile: VerificationFile,
  payrixService: PayrixService,
  payrixEntityId: string,
  requestId: string,
  verificationMethod: string,
  userAccountId?: string
): Promise<VerificationUploadResult> {
  try {
    if (!userAccountId) {
      logger.warn("User account creation failed or incomplete - proceeding with bank verification without login context", {
        requestId,
        payrixEntityId,
      });
    }

    logger.info("Processing bank verification upload (priority operation)", {
      requestId,
      payrixEntityId,
      verificationMethod,
      fileName: verificationFile.name,
      fileSize: verificationFile.size,
      userAccountId,
      hasLoginContext: !!userAccountId,
    });

    const result = await processBankVerification(payrixService, payrixEntityId, verificationFile, requestId, verificationMethod, userAccountId);

    logger.info("Bank verification upload completed successfully", {
      requestId,
      payrixEntityId,
      success: result.success,
      noteId: result.noteId,
      documentId: result.documentId,
    });

    return result;
  } catch (verificationError) {
    const errorMessage = verificationError instanceof Error ? verificationError.message : String(verificationError);
    logger.error("Bank verification upload failed", {
      requestId,
      payrixEntityId,
      error: verificationError,
      errorMessage,
      errorStack: verificationError instanceof Error ? verificationError.stack : undefined,
    });

    return {
      success: false,
      error: `Bank verification failed: ${errorMessage}`,
    };
  }
}

export async function orchestrateOnboarding(data: OnboardingRequest, requestId: string): Promise<OnboardingResult> {
  logger.info("Processing direct Payrix submission", {
    requestId,
    email: data.email,
    legalName: data.name,
    clientIp: data.clientIp,
  });

  const payrixService = new PayrixService();

  try {
    const merchantData = prepareMerchantData(data, requestId);
    const payrixResponse = await createMerchantInPayrix(payrixService, merchantData, requestId);
    const payrixEntityId = payrixResponse.id;

    if (!payrixEntityId) {
      throw new Error("Payrix response did not contain entity ID");
    }

    logger.info("Payrix merchant created successfully (direct integration)", {
      requestId,
      payrixEntityId,
    });

    const userAccountResult = await createUserAccount(data, payrixEntityId, requestId);
    const verificationResult = await processVerification(data, payrixService, payrixEntityId, requestId, userAccountResult?.data?.id);

    return {
      success: true,
      response: buildSuccessResponse(
        data,
        payrixResponse,
        payrixEntityId,
        userAccountResult.success ? userAccountResult.data || null : null,
        verificationResult
      ),
    };
  } catch (payrixError) {
    return {
      success: false,
      response: handlePayrixError(payrixError as Error, requestId),
    };
  }
}
