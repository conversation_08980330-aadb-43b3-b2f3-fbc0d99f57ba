import { APIGatewayProxyHandler } from "aws-lambda";
import { PayrixService } from "../../service/payrix.service.js";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { z } from "zod";

const CreatePlaidLinkTokenRequestSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  countryCode: z.enum(["US", "CA"], {
    errorMap: () => ({ message: "Country code must be US or CA" }),
  }),
  redirectUri: z.string().url("Redirect URI must be a valid URL"),
});

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;

  try {
    logger.info("Processing create Plaid link token request", { requestId });

    if (!event.body) {
      return createErrorResponse(400, "Request body is required", requestId);
    }

    let requestData;
    try {
      requestData = JSON.parse(event.body);
    } catch (parseError) {
      logger.error("Invalid JSON in request body", { requestId, error: parseError });
      return createErrorResponse(400, "Invalid JSON in request body", requestId);
    }

    const validationResult = CreatePlaidLinkTokenRequestSchema.safeParse(requestData);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => `${err.path.join(".")}: ${err.message}`);
      logger.error("Validation failed", { requestId, errors });
      return createErrorResponse(400, `Validation failed: ${errors.join(", ")}`, requestId);
    }

    const { userId, countryCode, redirectUri } = validationResult.data;

    const payrixService = new PayrixService();

    logger.info("Creating Plaid link token via Payrix", {
      requestId,
      userId,
      countryCode,
      redirectUri,
    });

    const plaidResponse = await payrixService.createPlaidLinkToken({
      userId,
      countryCode,
      redirectUri,
    });

    if (!plaidResponse?.linkToken) {
      throw new Error("Payrix response did not contain link token");
    }

    logger.info("Plaid link token created successfully", {
      requestId,
      userId,
      hasLinkToken: !!plaidResponse.linkToken,
      payrixRequestId: plaidResponse.requestId,
    });

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
      },
      body: JSON.stringify({
        success: true,
        message: "Plaid link token created successfully",
        data: {
          linkToken: plaidResponse.linkToken,
          requestId: plaidResponse.requestId,
          userId,
          countryCode,
          createdAt: new Date().toISOString(),
        },
      }),
    };
  } catch (error) {
    logger.error("Error creating Plaid link token", { requestId, error });

    // Handle specific Plaid/Payrix errors
    if (error instanceof Error) {
      if (error.message.includes("Invalid request parameters")) {
        return createErrorResponse(400, "Invalid request parameters for Plaid integration", requestId);
      } else if (error.message.includes("Authentication failed")) {
        return createErrorResponse(401, "Authentication failed with payment processor", requestId);
      } else if (error.message.includes("Plaid integration not enabled")) {
        return createErrorResponse(403, "Plaid integration is not enabled for this account", requestId);
      }
    }

    return createErrorResponse(500, "Internal server error", requestId);
  }
};
