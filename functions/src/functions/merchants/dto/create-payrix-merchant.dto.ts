export class CreatePayrixMerchantDto {
  // Corresponds to the top-level /entities fields
  name!: string; // Legal Business Name
  address1!: string;
  address2?: string;
  city!: string;
  state!: string;
  zip!: string;
  country!: string;
  phone!: string;
  email!: string;
  ein!: string;
  website!: string;
  type!: number; // 2 for Merchant
  public!: number; // 0 for private, 1 for public
  status!: number; // 1 for ready to board
  tcVersion!: string;
  tcDate!: string; // YYYYMMDDHHMM format for Payrix
  clientIp?: string;
  currency!: string; // e.g., 'USD'

  // Corresponds to the nested 'accounts' array
  accounts!: (
    | {
        primary: number; // 1 for true, 0 for false
        currency?: string; // USD
        account: {
          method: number; // 8=personal checking, 9=personal savings, 10=corporate checking, 11=corporate savings
          number: string;
          routing: string;
        };
      }
    | {
        primary: number; // 1 for true, 0 for false
        publicToken: string; // Plaid public token
        accountToken: string; // Plaid account token
        platform: "PLAID"; // Platform identifier
      }
  )[];

  // Corresponds to the nested 'merchant' object
  merchant!: {
    dba: string; // Doing Business As
    new: number; // 1 for first time digital payments, 0 for existing
    mcc: string; // Merchant Category Code (4 digits)
    status: string; // "1" for ready to board
    annualCCSales?: number; // Required for Payrix
    avgTicket?: number; // Required for Payrix
    established?: string; // YYYYMMDD
    members: {
      title: string;
      first: string;
      last: string;
      ssn?: string;
      dob: string; // YYYYMMDD format for Payrix
      dl?: string;
      dlstate?: string;
      ownership: number; // In basis points (e.g., 10000 = 100%)
      significantResponsibility: number; // 1 for true, 0 for false
      politicallyExposed: number; // 1 for true, 0 for false
      email: string;
      phone: string;
      primary: string; // '1' for true
      address1: string;
      address2?: string;
      city: string;
      state: string;
      zip: string;
      country: string;
    }[];
  };
}
