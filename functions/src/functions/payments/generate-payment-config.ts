import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { PayrixService } from "../../service/payrix.service.js";

interface PaymentConfigRequest {
  merchantId: string;
  description: string;
  amount?: number;
}

interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  returnUrl?: string;
}

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info("Generate payment config request", {
      body: event.body,
      headers: event.headers,
    });

    if (!event.body) {
      return createResponse(400, {
        error: "Request body is required",
        message: "Please provide merchantId and description",
      });
    }

    const { merchantId, description, amount }: PaymentConfigRequest = JSON.parse(event.body);

    // Validate required fields
    if (!merchantId || !description) {
      return createResponse(400, {
        error: "Missing required fields",
        message: "merchantId and description are required",
      });
    }

    // Validate merchantId format (basic validation)
    if (typeof merchantId !== "string" || merchantId.trim().length === 0) {
      return createResponse(400, {
        error: "Invalid merchantId",
        message: "merchantId must be a non-empty string",
      });
    }

    // Validate amount if provided
    if (amount !== undefined && (typeof amount !== "number" || amount < 0)) {
      return createResponse(400, {
        error: "Invalid amount",
        message: "amount must be a non-negative number",
      });
    }

    logger.info("Validating merchant with Payrix", {
      merchantId,
      description,
      amount,
    });

    // Validate merchant with Payrix API
    const payrixService = new PayrixService();
    const validation = await payrixService.validateMerchantById(merchantId);

    if (!validation.isValid) {
      logger.warn("Merchant validation failed", {
        merchantId,
        error: validation.error,
      });

      return createResponse(404, {
        error: "Merchant validation failed",
        message: validation.error || "Invalid or inactive merchant",
        details: {
          merchantId,
          validationError: validation.error,
        },
      });
    }

    logger.info("Merchant validation successful, generating PayFields config", {
      merchantId,
      merchantName: validation.merchant?.name,
    });

    // Get Payrix public API key from environment
    const payrixPublicKey = process.env.PAYRIX_PUBLIC_API_KEY || "default-public-key";

    // Prepare PayFields configuration for token-based flow
    const config: PayFieldsConfig = {
      merchantId,
      publicKey: payrixPublicKey,
      amount: 0, // $0 authorization for tokenization (will be converted to string in frontend)
      description,
      mode: "token", // Token-only mode for secure payment method storage
      txnType: "auth", // $0 authorization for payment method validation
    };

    logger.info("PayFields token configuration generated successfully", {
      merchantId: config.merchantId,
      amount: config.amount,
      description: config.description,
      mode: config.mode,
      txnType: config.txnType,
      note: "Token-based flow: PayFields will perform $0 auth and create secure token",
    });

    return createResponse(200, {
      config,
      message: "Payment configuration generated successfully",
      merchantInfo: {
        id: validation.merchant?.id,
        name: validation.merchant?.name,
        status: validation.merchant?.status,
      },
    });
  } catch (error) {
    logger.error("Error generating payment config", { error });

    return createResponse(500, {
      error: "Internal server error",
      message: "Failed to generate payment configuration",
    });
  }
};
