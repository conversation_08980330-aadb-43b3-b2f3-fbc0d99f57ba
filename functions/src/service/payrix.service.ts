import { type OnboardingRequest } from "../functions/merchants/schemas/onboarding.schema.js";
import { PayrixMerchantService } from "./payrix/merchant.service.js";
import { PayrixUserService } from "./payrix/user.service.js";
import { PayrixPaymentService } from "./payrix/payment.service.js";
import {
  PayrixMerchantResponse,
  PayrixUserResponse,
  TokenPaymentData,
  UserAccountData,
  PaymentResult,
  MerchantValidationResult,
  TokenDeletionResult,
} from "../types/payrix.types.js";

export class PayrixService {
  private merchantService: PayrixMerchantService;
  private userService: PayrixUserService;
  private paymentService: PayrixPaymentService;

  constructor() {
    this.merchantService = new PayrixMerchantService();
    this.userService = new PayrixUserService();
    this.paymentService = new PayrixPaymentService();
  }

  async checkMerchantExists(email: string, ein?: string): Promise<boolean> {
    return this.merchantService.checkMerchantExists(email, ein);
  }

  async validateMerchantById(merchantId: string): Promise<MerchantValidationResult> {
    return this.merchantService.validateMerchantById(merchantId);
  }

  async createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse> {
    return this.merchantService.createMerchant(merchantData);
  }

  async createUserAccount(userData: UserAccountData): Promise<PayrixUserResponse> {
    return this.userService.createUserAccount(userData);
  }

  async processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult> {
    return this.paymentService.processTokenPayment(paymentData);
  }

  async deleteToken(token: string): Promise<TokenDeletionResult> {
    return this.paymentService.deleteToken(token);
  }

  async createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }> {
    return this.merchantService.createNote(noteData);
  }

  async createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }> {
    return this.merchantService.createNoteDocument(documentData);
  }

  async createPlaidLinkToken(linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }): Promise<{ linkToken: string; requestId: string }> {
    return this.merchantService.createPlaidLinkToken(linkTokenData);
  }
}
