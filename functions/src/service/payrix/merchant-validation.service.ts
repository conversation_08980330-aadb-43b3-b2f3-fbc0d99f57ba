import { AxiosError, AxiosInstance } from "axios";
import { logger } from "../../helpers/logger.js";
import { PayrixMerchantEntity, MerchantValidationResult } from "../../types/payrix.types.js";
import { createPayrixApiClient } from "./api-client.js";
import { isApprovedMCCCode, getMCCCodeDetails } from "../../constants/approvedMccCodes.js";

export class PayrixMerchantValidationService {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = createPayrixApiClient();
  }

  async checkMerchantExists(email: string, ein?: string): Promise<boolean> {
    try {
      logger.info("Checking for existing merchant", {
        email,
        ein: ein ? "[REDACTED]" : undefined,
      });

      const response = await this.apiClient.get(`/entities`);

      logger.info("Merchant existence check response", {
        status: response.status,
        dataLength: response.data?.response?.data?.length || 0,
      });

      const merchants: PayrixMerchantEntity[] = response.data?.response?.data || [];
      const emailExists = merchants.some((merchant: PayrixMerchantEntity) => merchant.email?.toLowerCase() === email.toLowerCase());

      return emailExists;
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error checking merchant existence", {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      logger.warn("Merchant existence check failed, allowing onboarding to proceed");
      return false;
    }
  }

  async validateMerchantById(merchantId: string): Promise<MerchantValidationResult> {
    try {
      logger.info("Validating merchant by ID", { merchantId });

      const response = await this.apiClient.get(`/merchants/${merchantId}`);

      logger.info("Merchant validation response", {
        status: response.status,
        merchantId: response.data,
      });

      const merchant = response.data?.response?.data?.[0];

      if (!merchant) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      const validationResult = this.validateMerchantStatus(merchant);
      if (!validationResult.isValid) {
        return validationResult;
      }

      const mccValidation = this.validateMerchantMCC(merchant, merchantId);
      if (!mccValidation.isValid) {
        return mccValidation;
      }

      logger.info("Merchant validation successful", {
        merchantId,
        status: merchant.status,
        statusType: this.getStatusType(merchant.status),
        inactive: merchant.inactive,
        frozen: merchant.frozen,
        autoBoarded: merchant.autoBoarded,
        dba: merchant.dba,
        mcc: merchant.mcc,
      });

      return {
        isValid: true,
        merchant,
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      logger.error("Error validating merchant by ID", {
        merchantId,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      if (axiosError.response?.status === 404) {
        return {
          isValid: false,
          error: "Merchant not found",
        };
      }

      return {
        isValid: false,
        error: `Validation failed: ${axiosError.message}`,
      };
    }
  }

  private validateMerchantStatus(merchant: PayrixMerchantEntity): MerchantValidationResult {
    const isActive = (merchant.status === 1 || merchant.status === 2) && merchant.inactive === 0 && merchant.frozen === 0;

    if (!isActive) {
      const statusInfo = {
        status: merchant.status,
        inactive: merchant.inactive,
        frozen: merchant.frozen,
        autoBoarded: merchant.autoBoarded,
      };

      logger.warn("Merchant status check failed", statusInfo);

      return {
        isValid: false,
        error: `Merchant is not active (status: ${merchant.status}, inactive: ${merchant.inactive}, frozen: ${merchant.frozen})`,
      };
    }

    return { isValid: true };
  }

  private validateMerchantMCC(merchant: PayrixMerchantEntity, merchantId: string): MerchantValidationResult {
    const mccCode = merchant.mcc as string;
    if (mccCode && !isApprovedMCCCode(mccCode)) {
      const mccDetails = getMCCCodeDetails(mccCode);
      logger.warn("Merchant MCC validation failed", {
        merchantId,
        mcc: merchant.mcc,
        mccDescription: mccDetails?.description,
      });

      return {
        isValid: false,
        error: `Merchant MCC code ${merchant.mcc} is not approved for payment processing. Only specific business categories are currently supported.`,
      };
    }

    return { isValid: true };
  }

  private getStatusType(status: number): string {
    if (status === 1) return "fully-approved";
    if (status === 2) return "auto-boarded";
    return "unknown";
  }
}
