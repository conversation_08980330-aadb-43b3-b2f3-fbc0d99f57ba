import { AxiosError, AxiosInstance } from "axios";
import FormData from "form-data";
import { logger } from "../../helpers/logger.js";
import { createPayrixApiClient, PAYRIX_CREDENTIAL_ID } from "./api-client.js";

export class PayrixNoteManagementService {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = createPayrixApiClient();
  }

  async createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }> {
    try {
      logger.info("Creating note in Payrix", {
        entity: noteData.entity,
        type: noteData.type,
        noteLength: noteData.note.length,
      });

      const response = await this.apiClient.post("/notes", noteData);

      logger.info("Payrix note creation response", {
        status: response.status,
        data: response.data,
      });

      const noteResponse = response.data?.response?.data?.[0];
      if (!noteResponse?.id) {
        throw new Error("Invalid Payrix response structure: no note ID found");
      }

      return { id: noteResponse.id };
    } catch (error) {
      logger.error("Error creating note in Payrix", { error });
      if (error instanceof AxiosError) {
        logger.error("Payrix API error details", {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
      }
      throw error;
    }
  }

  async createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }> {
    try {
      logger.info("Creating note document in Payrix (3-step process)", {
        noteId: documentData.note,
        fileName: documentData.file.filename,
        fileSize: documentData.file.content.length,
        contentType: documentData.file.contentType,
      });

      const noteDocumentId = await this.createNoteDocumentMetadata(documentData);
      await this.uploadFileToNoteDocument(noteDocumentId, documentData);

      return { id: noteDocumentId };
    } catch (error) {
      this.handleNoteDocumentError(error as AxiosError, documentData);
      throw error;
    }
  }

  private async createNoteDocumentMetadata(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<string> {
    const fileExtension = documentData.file.filename.split(".").pop()?.toLowerCase() || "png";

    const noteDocumentPayload = {
      note: documentData.note,
      type: fileExtension,
      documentType: "voidCheck",
      description: documentData.description || "Void check for bank account verification",
      name: documentData.file.filename,
    };

    logger.info("Step 1: Creating note document metadata", {
      noteId: documentData.note,
      payload: noteDocumentPayload,
    });

    const noteDocResponse = await this.apiClient.post("/noteDocuments", noteDocumentPayload);

    logger.info("Note document metadata created", {
      status: noteDocResponse.status,
      responseData: noteDocResponse.data,
    });

    const noteDocumentId = noteDocResponse.data?.response?.data?.[0]?.id;
    if (!noteDocumentId) {
      throw new Error("Failed to create note document metadata - no ID returned");
    }

    return noteDocumentId;
  }

  private async uploadFileToNoteDocument(
    noteDocumentId: string,
    documentData: {
      note: string;
      file: { filename: string; content: Buffer; contentType: string };
      description?: string;
    }
  ): Promise<void> {
    logger.info("Step 2: Uploading file to note document", {
      noteDocumentId,
      fileName: documentData.file.filename,
    });

    const formData = new FormData();

    formData.append("file", documentData.file.content, {
      filename: documentData.file.filename,
      contentType: documentData.file.contentType,
    });

    const uploadPayload = {
      credential: PAYRIX_CREDENTIAL_ID || "default_credential",
      integration: "WORLDPAY",
      direction: "upload",
      name: documentData.file.filename,
      description: documentData.description || "Void Check for Bank Account Verification",
    };

    formData.append("json", JSON.stringify(uploadPayload));

    const fileUploadResponse = await this.apiClient.post(`/files/noteDocuments/${noteDocumentId}`, formData, {
      headers: {
        ...formData.getHeaders(),
      },
    });

    logger.info("File uploaded successfully to Payrix", {
      noteDocumentId,
      fileName: documentData.file.filename,
      uploadStatus: fileUploadResponse.status,
    });
  }

  private handleNoteDocumentError(
    error: AxiosError,
    documentData: {
      note: string;
      file: { filename: string; content: Buffer; contentType: string };
      description?: string;
    }
  ): void {
    logger.error("Error in createNoteDocument 3-step process", {
      error,
      noteId: documentData.note,
      fileName: documentData.file.filename,
    });

    if (error instanceof AxiosError) {
      logger.error("Payrix API error details", {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });

      const status = error.response?.status;
      const payrixData = error.response?.data as Record<string, unknown> | undefined;

      if (status === 400) {
        throw new Error(`Payrix validation error: ${(payrixData?.message as string) || "Invalid request data"}`);
      } else if (status === 401 || status === 403) {
        throw new Error("Payrix authentication failed. Please check API credentials.");
      } else if (status === 404) {
        throw new Error("Note not found. Please ensure the note exists before uploading documents.");
      } else if (status === 413) {
        throw new Error("File too large for Payrix. Please use a smaller file.");
      } else if (status === 422) {
        throw new Error(`Payrix processing error: ${(payrixData?.message as string) || "Unable to process request"}`);
      } else if (status && status >= 500) {
        throw new Error("Payrix service temporarily unavailable. Please try again later.");
      }
    }
  }
}
