import { type OnboardingRequest } from "../../functions/merchants/schemas/onboarding.schema.js";
import { PayrixMerchantResponse, MerchantValidationResult } from "../../types/payrix.types.js";
import { PayrixMerchantValidationService } from "./merchant-validation.service.js";
import { PayrixMerchantCreationService } from "./merchant-creation.service.js";
import { PayrixNoteManagementService } from "./note-management.service.js";

export class PayrixMerchantService {
  private validationService: PayrixMerchantValidationService;
  private creationService: PayrixMerchantCreationService;
  private noteService: PayrixNoteManagementService;

  constructor() {
    this.validationService = new PayrixMerchantValidationService();
    this.creationService = new PayrixMerchantCreationService();
    this.noteService = new PayrixNoteManagementService();
  }

  async checkMerchantExists(email: string, ein?: string): Promise<boolean> {
    return this.validationService.checkMerchantExists(email, ein);
  }

  async validateMerchantById(merchantId: string): Promise<MerchantValidationResult> {
    return this.validationService.validateMerchantById(merchantId);
  }

  async createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse> {
    return this.creationService.createMerchant(merchantData);
  }

  async createNote(noteData: { 
    entity: string; 
    note: string; 
    type?: string; 
    login?: string 
  }): Promise<{ id: string }> {
    return this.noteService.createNote(noteData);
  }

  async createPlaidLinkToken(linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }): Promise<{ linkToken: string; requestId: string }> {
    return this.creationService.createPlaidLinkToken(linkTokenData);
  }

  async createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }> {
    return this.noteService.createNoteDocument(documentData);
  }
}