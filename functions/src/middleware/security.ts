import { APIGatewayProxyEvent, APIGatewayProxyResult, APIGatewayProxyHandler } from "aws-lambda";
import {
  ValidationResult,
  sanitizeInput,
  validateTokenFormat,
  validateMerchantIdFormat,
  validateDescription,
  validateAmount,
  validateReturnUrl,
} from "./input-validators.js";
import { getCorsHeaders, isOriginAllowed, getAllowedOrigins } from "./cors-config.js";
import { createIframeResponse } from "../helpers/response.js";
import { logger } from "../helpers/logger.js";

export {
  ValidationResult,
  sanitizeInput,
  validateTokenFormat,
  validateMerchantIdFormat,
  validateDescription,
  validateAmount,
  validateReturnUrl,
  getCorsHeaders,
  isOriginAllowed,
  getAllowedOrigins,
};

export function validateRequest(event: APIGatewayProxyEvent): ValidationResult {
  if (!event.body) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Missing request body",
      message: "Request body is required",
    };
  }

  try {
    JSON.parse(event.body);
  } catch {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid JSON",
      message: "Request body must be valid JSON",
    };
  }

  return { isValid: true };
}

export function validatePaymentRequest(data: {
  token?: string;
  merchantId?: string;
  description?: string;
  amount?: number;
  returnUrl?: string;
}): ValidationResult {
  if (!data.token) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Missing token",
      message: "Token is required",
    };
  }

  const tokenValidation = validateTokenFormat(data.token);
  if (!tokenValidation.isValid) {
    return tokenValidation;
  }

  if (data.merchantId) {
    const merchantValidation = validateMerchantIdFormat(data.merchantId);
    if (!merchantValidation.isValid) {
      return merchantValidation;
    }
  }

  if (data.description) {
    const descriptionValidation = validateDescription(data.description);
    if (!descriptionValidation.isValid) {
      return descriptionValidation;
    }
  }

  if (data.amount !== undefined) {
    const amountValidation = validateAmount(data.amount);
    if (!amountValidation.isValid) {
      return amountValidation;
    }
  }

  if (data.returnUrl) {
    const urlValidation = validateReturnUrl(data.returnUrl);
    if (!urlValidation.isValid) {
      return urlValidation;
    }
  }

  return { isValid: true };
}

export function withIframeSecurity(handler: (event: APIGatewayProxyEvent) => Promise<APIGatewayProxyResult>): APIGatewayProxyHandler {
  return async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      if (event.httpMethod === "OPTIONS") {
        return createIframeResponse(200, {
          message: "CORS preflight successful",
        });
      }

      const origin = event.headers?.origin || event.headers?.Origin || "";
      logger.info("Iframe request received", {
        method: event.httpMethod,
        path: event.path,
        origin,
        userAgent: event.headers?.["User-Agent"] || event.headers?.["user-agent"],
      });

      const result = await handler(event);

      logger.info("Iframe request completed", {
        statusCode: result.statusCode,
        origin,
      });

      return result;
    } catch (error) {
      logger.error("Iframe security wrapper error", { error });

      return createIframeResponse(500, {
        error: "Internal server error",
        message: "An unexpected error occurred",
      });
    }
  };
}
