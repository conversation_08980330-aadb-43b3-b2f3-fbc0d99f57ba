import { toast } from "sonner";
import { processTokenPayment } from "../../../services/api";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import { postMessageToParent } from "../utils/iframe-communication";

export const createPaymentSuccessHandler = (
  config: PayFieldsConfig,
  paymentInfo: PaymentInfo | null,
  billingAddress?: BillingAddress,
  onSuccess?: (response: unknown) => void
) => {
  return async (response: PaymentResponse) => {
    console.log("Payment successful:", response);

    // Extract token from PayFields response structure
    // PayFields returns: {data: Array(1), details: {...}, errors: Array(0)}
    // The token is typically in data[0].token or data[0].id
    let extractedToken: string | undefined;

    if (config.mode === "token") {
      // Try multiple possible token locations in the response
      if (response.token) {
        extractedToken = response.token;
      } else if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        const firstDataItem = response.data[0];
        extractedToken = firstDataItem?.token || firstDataItem?.id;
      } else if (response.details?.token) {
        extractedToken = response.details.token;
      } else if (response.details?.id) {
        extractedToken = response.details.id;
      }

      console.log("🔍 Token extraction attempt:", {
        mode: config.mode,
        responseStructure: {
          hasToken: !!response.token,
          hasData: !!response.data,
          dataLength: Array.isArray(response.data) ? response.data.length : 0,
          hasDetails: !!response.details,
          extractedToken: extractedToken ? extractedToken.substring(0, 8) + "..." : "NOT_FOUND",
        },
        fullResponse: response,
      });
    }

    if (config.mode === "token" && extractedToken) {
      // Use original payment amount from paymentInfo, not the $0 amount from config
      const actualAmount = paymentInfo?.amount || config.amount;

      console.log("✅ Token generated successfully:", {
        token: extractedToken.substring(0, 8) + "...",
        merchantId: config.merchantId,
        tokenizationAmount: config.amount, // $0 for tokenization
        actualPaymentAmount: actualAmount, // Original amount for payment
        mode: config.mode,
        txnType: config.txnType,
        note: "Token ready for backend processing with actual payment amount",
      });

      try {
        const paymentResult = await processTokenPayment({
          merchantId: config.merchantId,
          token: extractedToken,
          amount: actualAmount, // Use original amount, not config.amount (which is 0)
          description: paymentInfo?.description || config.description,
          customerInfo: billingAddress
            ? {
                name: `${billingAddress.firstName} ${billingAddress.lastName}`,
                email: billingAddress.email,
                address: {
                  line1: billingAddress.line1,
                  line2: billingAddress.line2,
                  city: billingAddress.city,
                  state: billingAddress.state,
                  zip: billingAddress.zip,
                  country: billingAddress.country,
                },
              }
            : undefined,
        });

        if (paymentResult.success) {
          toast.success("Payment processed successfully!");
          console.log("✅ Token payment completed successfully:", {
            transactionId: paymentResult.transaction?.id,
            status: paymentResult.transaction?.status,
            amount: paymentResult.transaction?.amount,
            merchantId: config.merchantId,
          });

          postMessageToParent("PAYMENT_SUCCESS", {
            data: {
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
              tokenProcessed: true,
            },
          });

          if (onSuccess) {
            onSuccess({
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
              tokenProcessed: true,
            });
          }
        } else {
          throw new Error(paymentResult.message || "Token payment processing failed");
        }
      } catch (error) {
        console.error("❌ Token payment processing failed:", {
          error: error instanceof Error ? error.message : "Unknown error",
          token: extractedToken?.substring(0, 8) + "...",
          merchantId: config.merchantId,
          amount: config.amount,
        });

        const errorMessage = error instanceof Error ? error.message : "Token payment processing failed";
        toast.error(`Payment failed: ${errorMessage}`);

        postMessageToParent("PAYMENT_ERROR", {
          error: errorMessage,
          tokenGenerated: true,
          tokenProcessingFailed: true,
        });

        if (onSuccess) {
          onSuccess({
            error: errorMessage,
            tokenGenerated: true,
            tokenProcessingFailed: true,
          });
        }
        throw error;
      }
    } else if (config.mode === "token" && !extractedToken) {
      // Token mode but no token received - this is an error
      const errorMessage = "Token generation failed: No token received from PayFields";
      console.error("❌ Token generation failed:", {
        mode: config.mode,
        txnType: config.txnType,
        response,
        merchantId: config.merchantId,
        tokenExtractionAttempted: true,
        responseStructure: {
          hasToken: !!response.token,
          hasData: !!response.data,
          dataLength: Array.isArray(response.data) ? response.data.length : 0,
          hasDetails: !!response.details,
        },
      });

      toast.error(errorMessage);
      postMessageToParent("PAYMENT_ERROR", {
        error: errorMessage,
        tokenGenerationFailed: true,
      });

      if (onSuccess) {
        onSuccess({
          error: errorMessage,
          tokenGenerationFailed: true,
        });
      }
      throw new Error(errorMessage);
    } else {
      // Non-token mode or successful processing
      toast.success("Payment processed successfully!");
      postMessageToParent("PAYMENT_SUCCESS", { data: response });
      if (onSuccess) onSuccess(response);
    }
  };
};

export const createPaymentFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: PaymentError) => {
    console.error("Payment failed:", err);

    let errorMessage = "Payment processing failed. Please try again.";
    if (err && Array.isArray(err.errors)) {
      const fieldErrors = err.errors.map((e) => `${e.field}: ${e.msg}`).join(", ");
      errorMessage = `Payment failed: ${fieldErrors}`;
    } else if (err && err.message) {
      errorMessage = err.message;
    }

    toast.error(errorMessage);
    postMessageToParent("PAYMENT_FAILURE", {
      error: errorMessage,
      details: err,
    });

    if (onFailure) onFailure(err);
  };
};

export const createValidationFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: unknown) => {
    console.log("Validation error:", err);

    const validationMessage = "Payment validation failed. Please check your card details.";
    toast.error("Please check your card details");

    postMessageToParent("PAYMENT_VALIDATION_FAILURE", {
      error: validationMessage,
      details: err,
    });

    if (onFailure) onFailure({ message: validationMessage, details: err });
  };
};

export const createPaymentFinishHandler = () => {
  return (response: unknown) => {
    console.log("PayFields finished:", response);
    postMessageToParent("PAYMENT_FINISHED", { data: response });
  };
};
