import { useDispatch } from "react-redux";
import { useState } from "react";
import { toast } from "sonner";
import { updateFormData, nextStep } from "../../../redux/slices/onboardingSlice";
import { Member, DEFAULT_ADDITIONAL_MEMBER } from "../constants/ownerConstants";
import { validateOwnerForm } from "../utils/ownerValidation";
import { ensureSinglePrimaryContact } from "../utils/ownershipUtils";

interface FormData {
  merchant?: {
    dba?: string;
    new?: number;
    mcc?: string;
    status?: string;
    members?: Member[];
  };
  username?: string;
  password?: string;
  confirmPassword?: string;
}

export const useOwnerFormHandlers = (formData: FormData, members: Member[]) => {
  const dispatch = useDispatch();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const validationErrors = validateOwnerForm(
      members,
      formData.username,
      formData.password,
      formData.confirmPassword
    );
    setErrors(validationErrors);
    return validationErrors;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length === 0) {
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(validationErrors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);

      setTimeout(() => {
        const firstErrorKey = Object.keys(validationErrors)[0];
        if (firstErrorKey) {
          const errorElement = document.querySelector(`[data-error-key="${firstErrorKey}"]`);
          if (errorElement) {
            errorElement.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      }, 100);
    }
  };

  const handleChange = (memberIndex: number, field: string, value: string | number) => {
    const errorKey = `member${memberIndex}.${field}`;
    if (errors[errorKey]) {
      const newErrors = { ...errors };
      delete newErrors[errorKey];
      setErrors(newErrors);
    }

    const updatedMembers = [...members];
    updatedMembers[memberIndex] = {
      ...updatedMembers[memberIndex],
      [field]: value,
    };

    dispatch(
      updateFormData({
        merchant: {
          dba: "",
          new: formData.merchant?.new ?? 1,
          mcc: "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  const addOwner = () => {
    const newMember = { ...DEFAULT_ADDITIONAL_MEMBER, primary: "0" };
    const updatedMembers = ensureSinglePrimaryContact([...members, newMember]);
    dispatch(
      updateFormData({
        merchant: {
          dba: formData.merchant?.dba || "",
          new: formData.merchant?.new ?? 1,
          mcc: formData.merchant?.mcc || "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  const handlePrimaryChange = (selectedIndex: number) => {
    const updatedMembers = ensureSinglePrimaryContact(members, selectedIndex);
    dispatch(
      updateFormData({
        merchant: {
          dba: formData.merchant?.dba || "",
          new: formData.merchant?.new ?? 1,
          mcc: formData.merchant?.mcc || "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  const removeOwner = (index: number) => {
    if (members.length > 1) {
      const wasRemovedMemberPrimary = members[index].primary === "1";
      const filteredMembers = members.filter((_, i) => i !== index);

      const updatedMembers = wasRemovedMemberPrimary
        ? ensureSinglePrimaryContact(filteredMembers, 0)
        : ensureSinglePrimaryContact(filteredMembers);

      dispatch(
        updateFormData({
          merchant: {
            dba: formData.merchant?.dba || "",
            new: formData.merchant?.new ?? 1,
            mcc: formData.merchant?.mcc || "",
            status: "1",
            ...formData.merchant,
            members: updatedMembers,
          },
        })
      );
    }
  };

  return {
    errors,
    setErrors,
    validateForm,
    handleSubmit,
    handleChange,
    addOwner,
    handlePrimaryChange,
    removeOwner,
  };
};