export interface Member {
  title: string;
  first: string;
  middle: string;
  last: string;
  ssn: string;
  dob: string;
  dl: string;
  dlstate: string;
  ownership: number;
  significantResponsibility: number;
  politicallyExposed: number;
  email: string;
  phone: string;
  primary: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

export const DEFAULT_MEMBER: Member = {
  title: "",
  first: "",
  middle: "",
  last: "",
  ssn: "",
  dob: "",
  dl: "",
  dlstate: "",
  ownership: 10000, // Will be auto-redistributed when multiple members are added
  significantResponsibility: 1,
  politicallyExposed: 0,
  email: "",
  phone: "",
  primary: "1",
  address1: "",
  address2: "",
  city: "",
  state: "",
  zip: "",
  country: "USA",
};

export const DEFAULT_ADDITIONAL_MEMBER: Member = {
  title: "",
  first: "",
  middle: "",
  last: "",
  ssn: "",
  dob: "",
  dl: "",
  dlstate: "",
  ownership: 2500, // Default to 25% - user must manually adjust to ensure 100% total
  significantResponsibility: 0,
  politicallyExposed: 0,
  email: "",
  phone: "",
  primary: "0", // New members are never primary by default
  address1: "",
  address2: "",
  city: "",
  state: "",
  zip: "",
  country: "USA",
};

export const OWNERSHIP_CONSTANTS = {
  FULL_OWNERSHIP: 10000,
  MINIMUM_REQUIRED_OWNERSHIP: 2500,
  PERCENTAGE_DIVISOR: 100,
} as const;

export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\d{10}$/,
  SSN: /^\d{9}$/,
  ZIP: /^\d{5}(-\d{4})?$/,
  STATE: /^[A-Z]{2}$/,
  PO_BOX: /^(p\.?\s?o\.?\s?box|post\s?office\s?box)/i,
} as const;

export const MINIMUM_AGE = 18;
