import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { prevStep } from "../../redux/slices/onboardingSlice.ts";
import { FinCenNotice, OwnershipSummary, OwnerFormSection, AccountCreationSection } from "./sections";
import { Member, DEFAULT_MEMBER } from "./constants/ownerConstants";
import { useOwnerFormHandlers } from "./hooks/useOwnerFormHandlers";
import { useAccountFieldValidation } from "./hooks/useAccountFieldValidation";
import { ErrorSummary } from "./components/ErrorSummary";

const OwnerInfoFormMultiple = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  
  const members = (formData.merchant?.members as Member[]) || [DEFAULT_MEMBER];
  
  const {
    errors,
    setErrors,
    handleSubmit,
    handleChange,
    addOwner,
    handlePrimaryChange,
    removeOwner,
  } = useOwnerFormHandlers(formData, members);
  
  const { handleAccountFieldChange } = useAccountFieldValidation(formData, errors, setErrors);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Owner Information</h1>
            <p className="text-gray-600 mt-1">Details about business owners and principals</p>
          </div>

          {/* FINCEN Language */}
          <div className="px-8 pt-6">
            <FinCenNotice />
          </div>

          <form onSubmit={handleSubmit} className="px-8 pb-8">
            <ErrorSummary errors={errors} />
            {/* Total Ownership Display */}
            <OwnershipSummary members={members} />

            {/* Multiple Owners Question */}
            <div className="mb-8">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 className="text-sm font-medium text-gray-900 mb-4">Does any other principal own 25% or more of the business?</h3>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={addOwner}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    Yes, Add Additional Principal
                  </button>
                  {members.length === 1 && <span className="text-sm text-gray-600 py-2">Currently showing 1 principal</span>}
                  {members.length > 1 && <span className="text-sm text-gray-600 py-2">Currently showing {members.length} principals</span>}
                </div>
              </div>
            </div>

            {/* Owner Forms */}
            {members.map((member, index) => (
              <OwnerFormSection
                key={index}
                member={member}
                index={index}
                canRemove={members.length > 1}
                onFieldChange={handleChange}
                onRemove={removeOwner}
                onPrimaryChange={handlePrimaryChange}
                errors={errors}
              />
            ))}

            {/* Account Creation */}
            <AccountCreationSection
              username={formData.username || ""}
              password={formData.password || ""}
              confirmPassword={formData.confirmPassword || ""}
              onFieldChange={handleAccountFieldChange}
              errors={{
                username: errors.username,
                password: errors.password,
                confirmPassword: errors.confirmPassword,
              }}
            />

            {/* Navigation Buttons */}
            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Continue to Bank Account
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OwnerInfoFormMultiple;
