import { useState } from "react";
import { toast } from "sonner";
import type { PaymentInfo } from "../types/payment";
import { formatSuccessMessage, formatErrorMessage } from "../utils/paymentUtils";

interface UsePaymentHandlersReturn {
  success: boolean;
  error: string | null;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  handlePaymentSuccess: (response: unknown) => void;
  handlePaymentFailure: (error: unknown) => void;
}

/**
 * Custom hook for handling payment success and failure events
 */
export const usePaymentHandlers = (paymentInfo: PaymentInfo | null): UsePaymentHandlersReturn => {
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (response: unknown) => {
    console.log("Payment successful:", response);
    setSuccess(true);
    toast.success("Payment processed successfully!");

    // Notify parent window of success
    if (window.parent !== window) {
      window.parent.postMessage(formatSuccessMessage(response, "PAYMENT_SUCCESS"), "*");
    }

    // Handle redirect if specified
    // if (paymentInfo?.returnUrl) {
    //   setTimeout(() => {
    //     if (window.parent !== window) {
    //       window.parent.postMessage(
    //         formatSuccessMessage(paymentInfo.returnUrl, "PAYMENT_REDIRECT", {
    //           url: paymentInfo.returnUrl,
    //         }),
    //         "*"
    //       );
    //     } else {
    //       window.location.href = paymentInfo.returnUrl!;
    //     }
    //   }, 2000);
    // }
  };

  const handlePaymentFailure = (error: unknown) => {
    console.error("Payment failed:", error);
    const errorMessage = error instanceof Error ? error.message : (error as { message?: string })?.message || "Payment processing failed";

    setError(errorMessage);
    toast.error(errorMessage);

    // Notify parent window of failure
    if (window.parent !== window) {
      window.parent.postMessage(formatErrorMessage(error, "PAYMENT_FAILURE"), "*");
    }
  };

  return {
    success,
    error,
    setError,
    handlePaymentSuccess,
    handlePaymentFailure,
  };
};
