import type { AxiosResponse } from "axios";
import { apiClient } from "../config";
import type {
  CreatePayrixMerchantRequest,
  MerchantResponse,
  CreateNoteRequest,
  CreateNoteResponse,
  CreateNoteDocumentRequest,
  CreateNoteDocumentResponse,
  CreatePlaidLinkTokenRequest,
  CreatePlaidLinkTokenResponse,
  ProcessPlaidAccountRequest,
  ProcessPlaidAccountResponse,
} from "../types/merchant";

export const createMerchant = async (merchantData: CreatePayrixMerchantRequest): Promise<MerchantResponse> => {
  try {
    const response: AxiosResponse<MerchantResponse> = await apiClient.post("/merchants/onboard", merchantData);
    return response.data;
  } catch (error) {
    console.error("Error creating merchant:", error);
    throw error;
  }
};

export const createNote = async (noteData: CreateNoteRequest): Promise<CreateNoteResponse> => {
  try {
    const response: AxiosResponse<CreateNoteResponse> = await apiClient.post("/merchants/notes", noteData);
    return response.data;
  } catch (error) {
    console.error("Error creating note:", error);
    throw error;
  }
};

export const createNoteDocument = async (documentData: CreateNoteDocumentRequest): Promise<CreateNoteDocumentResponse> => {
  try {
    const formData = new FormData();
    formData.append("noteId", documentData.noteId);
    formData.append("file", documentData.file);
    if (documentData.description) {
      formData.append("description", documentData.description);
    }

    const response: AxiosResponse<CreateNoteDocumentResponse> = await apiClient.post("/merchants/note-documents", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error creating note document:", error);
    throw error;
  }
};

export const createPlaidLinkToken = async (tokenData: CreatePlaidLinkTokenRequest): Promise<CreatePlaidLinkTokenResponse> => {
  try {
    const response: AxiosResponse<CreatePlaidLinkTokenResponse> = await apiClient.post("/merchants/plaid/link-token", tokenData);
    return response.data;
  } catch (error) {
    console.error("Error creating Plaid link token:", error);
    throw error;
  }
};

export const processPlaidAccount = async (accountData: ProcessPlaidAccountRequest): Promise<ProcessPlaidAccountResponse> => {
  try {
    const response: AxiosResponse<ProcessPlaidAccountResponse> = await apiClient.post("/merchants/plaid/process-account", accountData);
    return response.data;
  } catch (error) {
    console.error("Error processing Plaid account:", error);
    throw error;
  }
};
